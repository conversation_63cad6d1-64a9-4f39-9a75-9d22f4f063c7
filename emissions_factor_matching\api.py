import numpy as np
from typing import Optional
from pydantic import BaseModel
from fastapi import (
    FastAPI,
    Header,
    Depends,
    Query,
    HTTPException,
)
from torch import tensor
import json
from emissions_factor_matching.dataset import collection, collection_eol
from emissions_factor_matching.predictions import (
    get_cas_number,
    predict_enhanced_input_category,
    spot_modifiers,
    map_isic_classification,
    augment_query_text,
    construct_dynamic_filters,
    re_rank_candidates,
    get_closest_match,
    get_technological_representation,
)
from emissions_factor_matching.dataset import search_candidates_with_fallback
from shared_predictions import get_top_match
from emissions_factor_matching.geography import get_geography_activity_match
from utils import logger
from utils.cache import create_cache, cached
import asyncio

cache = create_cache(prefix='ef_')

def get_api_version(api_version: Optional[str] = Header(default="latest")) -> str:
    return api_version

model_api = FastAPI()

def query_eol_activities(material: str, disposal_method: str, number_of_results: int = 25):
    where_document = (
        {"$contains":"Recycled Content cut-off"}
        if disposal_method == "recycling"
        else None
    )

    return collection_eol.query(
        query_texts=[f"{material} {disposal_method}"],
        n_results=number_of_results,
        where_document=where_document
    )

class ActivityRequest(BaseModel):
    chemical_name: str
    cas_number: str | None = None

class Activity(BaseModel):
    activity_uuid: str
    activity_name: str
    reference_product_name: str
    product_information: str
    source: str
    geography: str | None = None

class ActivityResponse(Activity):
    similarity: float | None = None

class ActivityRecommendationsRequest(BaseModel):
    # Frontend GraphQL parameters (match exactly)
    chemicalName: str  # Required - the entity/material/chemical to match
    productCategory: str | None = None
    casNo: str | None = None
    geography: str | None = None
    geographyModeling: bool | None = None
    unit: str | None = None
    lcaLifecycleStage: str | None = None
    labs: bool | None = None  # Lab-specific parameter
    
    # Legacy parameters for backward compatibility (from old API calls)
    chemical_name: str | None = None
    cas_number: str | None = None
    iso_code: str | None = None
    product_category: str | None = None

class ActivityRecommendationsResponse(BaseModel):
    matched_activity: Activity
    confidence: str
    explanation: str
    recommendations: list[ActivityResponse]

def get_activity_from_dataset(
    activity_name: str,
    iso_code: str,
    similarity: float,
    reference_product_name: str,
    preferred_source: str | None = None,
) -> ActivityResponse:
    activity = get_geography_activity_match(
        activity_name,
        iso_code,
        reference_product_name,
        preferred_source
    )

    return ActivityResponse(
        activity_name=activity["Activity Name"],
        activity_uuid=activity["Activity UUID"],
        reference_product_name=activity["Reference Product Name"],
        product_information=activity["Product Information"],
        source=activity["Source"],
        geography=activity["Geography"],
        similarity=similarity
    )

@model_api.get("/search")
@cached(cache)
def search_activities(
    query: str = Query(..., description="The search query"),
    geography_iso3: str = Query(default="GLO", description="The geography to search in"),
    number_of_results: int = Query(default=25, description="The number of results to return"),
    api_version: str = Depends(get_api_version),
) -> list[ActivityResponse]:
    logger.info(f"API Version: {api_version}")

    where = {"activity_type": {"$eq": "ordinary transforming activity"}}
    documents = collection.query(
        query_texts=[query],
        n_results=number_of_results,
        where=where,
    )

    activities = [
        get_activity_from_dataset(
            activity_name=documents["documents"][0][i],
            iso_code=geography_iso3,
            similarity=0,
            reference_product_name=documents["metadatas"][0][i]["reference_product_name"]
        )
        for i in range(len(documents["ids"][0]))
    ]

    return activities

class EolActivityRecommendationsRequest(BaseModel):
    material: str
    disposal_method: str
    geography: str = "RoW"

@model_api.post("/eol/activity")
@cached(cache)
async def get_eol_recommended_activity(
    activity_request: EolActivityRecommendationsRequest,
    api_version: str = Depends(get_api_version),
):
    logger.info(f"API Version: {api_version}")

    documents = query_eol_activities(
        material=activity_request.material,
        disposal_method=activity_request.disposal_method,
    )

    activities = []
    activities_for_selection = []
    for i in range(len(documents["ids"][0])):
        activity = {
            "uuid": documents["metadatas"][0][i]["uuid"],
            "activity_name": documents["documents"][0][i],
            "metadata": documents["metadatas"][0][i],
            "similarity": 0,
        }
        activities.append(activity)
        activities_for_selection.append({
            k: v
            for k, v in activity.items()
            if k != "similarity"
        })

    closest_match = await get_closest_match(
        f"{activity_request.material} ({activity_request.disposal_method})",
        activities_for_selection,
    )

    matched_activity = next(
        (
            get_activity_from_dataset(
                activity["activity_name"],
                activity_request.geography,
                activity["similarity"],
                activity["metadata"]["reference_product_name"]
            )
            for activity in activities
            if closest_match.get("activity_uuid") == activity["uuid"]
        ),
        None,
    )

    return matched_activity

@model_api.post("/activities/recommendations")
@cached(cache)
async def get_recommended_activities(
    activity_request: ActivityRecommendationsRequest,
    api_version: str = Depends(get_api_version),
):
    logger.info(f"API Version: {api_version}")
    logger.info("Phase 1: Enhanced Input Category Prediction - Starting")

    # Handle backward compatibility mapping
    primary_query = None

    # Priority: new parameter > legacy parameter
    if activity_request.chemicalName:
        primary_query = activity_request.chemicalName
    elif activity_request.chemical_name:
        primary_query = activity_request.chemical_name
        logger.info(f"Backward compatibility: using chemical_name")

    if not primary_query:
        raise HTTPException(status_code=400, detail="chemicalName or chemical_name is required")
    
    # Map geography parameter for internal processing (iso_code is used downstream)
    iso_code = activity_request.geography or activity_request.iso_code or "GLO"
    
    # Map product category for internal processing
    product_category = activity_request.productCategory or activity_request.product_category
    
    # Map CAS number for internal processing  
    cas_number = activity_request.casNo or activity_request.cas_number

    # Create internal request object with mapped parameters for downstream processing
    class InternalRequest(BaseModel):
        user_query_primary: str
        user_query_secondary: str | None = None
        lca_lifecycle_stage: str | None = None
        iso_code: str | None = None
        product_category_context: str | None = None
        cas_number: str | None = None
        
    internal_request = InternalRequest(
        user_query_primary=primary_query,
        user_query_secondary=None,  # Frontend doesn't send this yet
        lca_lifecycle_stage=activity_request.lcaLifecycleStage,
        iso_code=iso_code,
        product_category_context=product_category,
        cas_number=cas_number
    )

    # Phase 1.1: Enhanced Input Category Prediction
    enhanced_category = predict_enhanced_input_category(internal_request)
    logger.info(f"Phase 1 Complete: Enhanced category = {enhanced_category}")

    # Phase 1.2: Modifier Spotting
    logger.info("Phase 2: Modifier Spotting - Starting")
    modifiers = spot_modifiers(internal_request, enhanced_category)
    logger.info(f"Phase 2 Complete: Extracted {len(modifiers)} modifiers = {modifiers}")

    # Phase 1.3: ISIC Classification Mapping
    logger.info("Phase 3: ISIC Classification Mapping - Starting")
    isic_codes = map_isic_classification(enhanced_category, modifiers, internal_request.user_query_primary)
    logger.info(f"Phase 3 Complete: Mapped to {len(isic_codes)} ISIC codes = {isic_codes}")

    # Phase 1.4: Query Text Augmentation
    logger.info("Phase 4: Query Text Augmentation - Starting")
    augmented_query = augment_query_text(internal_request, enhanced_category, modifiers, isic_codes)
    logger.info(f"Phase 4 Complete: Augmented query = '{augmented_query}'")

    # Phase 1.5: Dynamic Filter Construction
    logger.info("Phase 5: Dynamic Filter Construction - Starting")
    dynamic_filters = construct_dynamic_filters(internal_request, enhanced_category, modifiers, isic_codes)
    logger.info(f"Phase 5 Complete: Dynamic filters = {dynamic_filters}")

    # Handle CAS number lookup if not provided
    if not cas_number and primary_query:
        cas_number = get_cas_number(primary_query)
        internal_request.cas_number = cas_number
    logger.info(f"CAS Number: {cas_number}")

    # Phase 1.6: ChromaDB Vector Search Enhancement
    logger.info("Phase 6: ChromaDB Vector Search Enhancement - Starting")
    candidates = search_candidates_with_fallback(
        augmented_query=augmented_query,
        filters=dynamic_filters,
        n_results=25
    )
    logger.info(f"Phase 6 Complete: Retrieved {len(candidates)} candidates")

    # Phase 1.7: LLM Re-ranking & Justification
    logger.info("Phase 7: LLM Re-ranking & Justification - Starting")
    if not candidates:
        raise HTTPException(status_code=404, detail="No emission factor candidates found")

    matched_ef = re_rank_candidates(
        request_model=internal_request,
        candidates=candidates,
        augmented_query=augmented_query,
        enhanced_category=enhanced_category,
        modifiers=modifiers,
        isic_codes=isic_codes
    )
    logger.info(f"Phase 7 Complete: Selected '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")

    # Phase 1.8: Geography Matching & Record Retrieval
    logger.info("Phase 8: Geography Matching & Record Retrieval - Starting")
    matched_activity = get_activity_from_dataset(
        activity_name=matched_ef.activity_name,
        iso_code=iso_code,
        similarity=matched_ef.confidence_score or 0.0,
        reference_product_name=matched_ef.reference_product_name,
        preferred_source=matched_ef.source  # Preserve the source selected by AI pipeline
    )
    logger.info(f"Phase 8 Complete: Geography matched to '{matched_activity.geography}' with source '{matched_activity.source}'")

    # Phase 1.9: Response Assembly
    logger.info("Phase 9: Response Assembly - Starting")

    # Create recommendations from remaining candidates (excluding the selected one)
    recommendations = []
    for candidate in candidates:
        if candidate.activity_uuid != matched_ef.activity_uuid:
            try:
                recommendation = get_activity_from_dataset(
                    activity_name=candidate.activity_name,
                    iso_code=iso_code,
                    similarity=candidate.similarity_score or 0.0,
                    reference_product_name=candidate.reference_product_name,
                    preferred_source=candidate.source  # Preserve the source from each candidate
                )
                recommendations.append(recommendation)
            except Exception as e:
                logger.warning(f"Failed to create recommendation for {candidate.activity_uuid}: {str(e)}")
                continue

    logger.info(f"Phase 9 Complete: Assembled response with {len(recommendations)} recommendations")

    return ActivityRecommendationsResponse(
        matched_activity=matched_activity,
        confidence=matched_ef.confidence,
        explanation=matched_ef.explanation,
        recommendations=recommendations,
    )

@model_api.get("/geography-match")
@cached(cache)
def geography_match(
    activity_name: str,
    reference_product_name: str,
    target_geography_iso: str,
    api_version: str = Depends(get_api_version),
) -> Activity:
    activity = get_geography_activity_match(
        activity_name,
        target_geography_iso,
        reference_product_name
    )

    return Activity(
        activity_name=activity["Activity Name"],
        activity_uuid=activity["Activity UUID"],
        reference_product_name=activity["Reference Product Name"],
        product_information=activity["Product Information"],
        source=activity["Source"],
        geography=activity["Geography"],
    )

@model_api.post("/cache/clear")
def clear_cache():
    """Clear the entire cache."""
    cache.clear()
    return {"message": "Cache cleared successfully"}

@model_api.get("/cache/stats")
def get_cache_stats():
    """Get cache statistics."""
    volume = cache.volume()
    size_kb = volume / 1024 if volume is not None else 0
    try:
        num_items = len(cache)
    except (TypeError, NotImplementedError):
        num_items = "Not available for this cache type"

    return {
        "size (KB)": size_kb,
        "number of items": num_items,
        "directory": cache.directory
    }

@model_api.get("/match-quality")
@cached(cache)
async def match_quality(process_name: str = Query(...), matched_activity_name: str = Query(...)):
    technology_match = await get_technological_representation(process_name, matched_activity_name)

    return {"match_quality": technology_match}
