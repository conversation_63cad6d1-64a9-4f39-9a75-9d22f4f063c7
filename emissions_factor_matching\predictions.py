from typing import Dict, Any, List
import json
import time
from emissions_factor_matching.dataset import efs_with_geographies
from emissions_factor_matching.prompt import get_enhanced_input_category_prompt, get_modifier_spotting_prompt, get_llm_reranking_prompt, get_isic_classification_prompt
from emissions_factor_matching.model import Candidate, MatchedEF
from emissions_factor_matching.models import (
    InputCategoryResponse,
    ModifiersResponse,
    ISICClassificationResponse,
    ClosestMatchResponse,
    TechnologicalRepresentationResponse,
    RerankingResponse
)
from completions import (
    get_chat_completion, 
    get_async_chat_completion,
    get_structured_completion,
    get_async_structured_completion
)
from clients import openai_client, openai_client_us_east, get_openai_async_client
from config import config
from utils import logger


def get_cas_number(chemical_name: str):
    prompt = (
        "Given a chemical return the cas number in the form (xxxxxx-xx-x).\n"
        "If the chemical does not have a CAS Number or is not a chemical return \"NONE\"\n\n"
        "Examples:\n\n"
        "Chemical: Sodium Chloride\n"
        "CAS No.: 7647-14-5\n\n"
        "Chemical: Koolaid\n\n"
        "CAS No.: NONE\n\n"
        "Chemical: Coffee\n"
        "CAS No.: NONE\n\n"
        "Chemical: Water\n"
        "CAS No.: 7732-18-5\n\n"
        "Note: Valid output should only either be a CAS No. or \"NONE\" with no additional output"
    )

    messages = [
        {
            "role": "system",
            "content": prompt,
        },
        {
            "role": "user",
            "content": (
                f"Chemical: {chemical_name}\n"
                "Cas No.: "
            )
        },
    ]

    return get_chat_completion(openai_client, messages, deployment=config.azure_openai_deployment, n_validations=1)


def get_common_chemical_names(chemical_name: str):
    prompt = (
        "Provide a comma seperated list of common chemical synonyms for a given ingredient.\n"
        "Avoid using informal names, they should be valid ingredient synonyms (max 5).\n"
        "If an ingredient is not a chemical respond with \"NONE\"\n"
        "Examples:\n\n"
        "Chemical: \"Sugar\"\n"
        "sucrose, glucose, fructose\n\n"
        "Chemical: \"Coffee\"\n"
        "NONE\n\n"
        "Chemical: \"Sodium Carbonate\"\n"
        "soda ash\n\n"
        "Chemical: \"Fish\"\n"
        "NONE\n\n"
        "Chemical: \"Strawberry\"\n"
        "NONE\n\n"
        "Chemical: \"Artificial Flavoring\"\n"
        "NONE\n\n"
        "Chemical: \"salt\"\n"
        "sodium chloride\n\n"
        "Chemical: \"mango\"\n"
        "NONE"
    )

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": (
                f"Chemical: {chemical_name}\n"
            )
        }
    ]

    return get_chat_completion(
        openai_client_us_east,
        messages,
        deployment=config.azure_openai_deployment,
        n_validations=0,
    )

def predict_input_category(input_str: str):
    """Legacy function for backward compatibility - calls enhanced version"""
    # For backward compatibility, convert single string to request-like structure
    from pydantic import BaseModel

    class TempRequest(BaseModel):
        user_query_primary: str
        user_query_secondary: str | None = None
        lca_lifecycle_stage: str | None = None
        iso_code: str | None = None

    request = TempRequest(user_query_primary=input_str)
    result = predict_enhanced_input_category(request)
    return result

def predict_enhanced_input_category(request: Any):
    """
    Predict the input category for the enhanced emissions factor matching.
    Now uses structured output with LangChain.
    """
    from pydantic import BaseModel

    # Extract fields from request
    if isinstance(request, BaseModel):
        user_query_primary = request.user_query_primary
        user_query_secondary = getattr(request, 'user_query_secondary', None)
        lca_lifecycle_stage = getattr(request, 'lca_lifecycle_stage', None)
        iso_code = getattr(request, 'iso_code', None)
    elif isinstance(request, dict):
        user_query_primary = request.get('user_query_primary')
        user_query_secondary = request.get('user_query_secondary')
        lca_lifecycle_stage = request.get('lca_lifecycle_stage')
        iso_code = request.get('iso_code')
    else:
        # Handle string input for backward compatibility
        user_query_primary = str(request)
        user_query_secondary = None
        lca_lifecycle_stage = None
        iso_code = None

    if not user_query_primary:
        logger.error("No primary query provided")
        return None

    prompt = get_enhanced_input_category_prompt()

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": f"User query: {user_query_primary}"
                      + (f"\nAdditional context: {user_query_secondary}" if user_query_secondary else "")
                      + (f"\nLCA lifecycle stage: {lca_lifecycle_stage}" if lca_lifecycle_stage else "")
                      + (f"\nISO country code: {iso_code}" if iso_code else "")
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=InputCategoryResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    if result:
        return result.category
    else:
        logger.warning("Failed to get structured response for input category")
        return None


def predict_category(input_str: str):
    """Alias for predict_input_category for backward compatibility"""
    return predict_input_category(input_str)


def spot_modifiers(request: Any, enhanced_category: str) -> List[str]:
    """
    Extracts modifiers from the input description.
    Now uses structured output with LangChain.
    """
    # Extract input from request object
    input_parts = []
    
    if hasattr(request, 'user_query_primary'):
        input_parts.append(request.user_query_primary)
    
    if hasattr(request, 'user_query_secondary') and request.user_query_secondary:
        input_parts.append(request.user_query_secondary)
    
    # Build the query
    query = f"Input: {' | '.join(input_parts)}"
    query += f"\nEnhanced Category: {enhanced_category}"
    
    # Add optional parameters if provided
    if hasattr(request, 'lca_lifecycle_stage') and request.lca_lifecycle_stage:
        query += f"\nLCA Lifecycle Stage: {request.lca_lifecycle_stage}"
    if hasattr(request, 'iso_code') and request.iso_code:
        query += f"\nISO Country Code: {request.iso_code}"

    prompt = get_modifier_spotting_prompt()

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": query
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=ModifiersResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    if result:
        return result.modifiers
    else:
        logger.warning("Failed to get structured response for modifiers")
        return []


def map_isic_classification(enhanced_category: str, modifiers: List[str], user_query_primary: str = None) -> List[str]:
    """
    Maps user query to ISIC codes based on enhanced category and modifiers.
    Now uses structured output with LangChain.
    """
    # Build the query with all context
    query_parts = []
    query_parts.append(f"Enhanced Category: {enhanced_category}")
    
    if modifiers:
        query_parts.append(f"Modifiers: {', '.join(modifiers)}")
    
    if user_query_primary:
        query_parts.append(f"User Query: {user_query_primary}")
    
    query = '\n'.join(query_parts)

    prompt = get_isic_classification_prompt()

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": query
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=ISICClassificationResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    if result:
        return result.result
    else:
        logger.warning("Failed to get structured response for ISIC classification")
        return []


def prepare_results(
    results_dict: Dict[str, List[Candidate]],
    modifier_used: bool,
    isic_codes: List[str],
) -> List[MatchedEF]:
    sorted_results = {}
    for phase_name, candidates in results_dict.items():
        sorted_candidates = sorted(candidates, key=lambda c: c.similarity_score, reverse=True)
        sorted_results[phase_name] = sorted_candidates

    # Flatten results from all phases
    all_results = []
    for phase_name, candidates in sorted_results.items():
        for candidate in candidates:
            all_results.append(
                MatchedEF(
                    activity_uuid=candidate.activity_uuid,
                    similarity_score=candidate.similarity_score,
                    ef_activity_name=candidate.ef_activity_name,
                    ef_reference_unit=candidate.ef_reference_unit,
                    phase=phase_name,
                    modifier_used=modifier_used,
                    isic_used=bool(isic_codes),
                )
            )

    # Sort all results by similarity score
    all_results = sorted(all_results, key=lambda r: r.similarity_score, reverse=True)

    # Keep top 15 overall
    top_results = all_results[:15]

    # Remove duplicates based on activity_uuid while preserving order
    seen_uuids = set()
    unique_results = []
    for result in top_results:
        if result.activity_uuid not in seen_uuids:
            seen_uuids.add(result.activity_uuid)
            unique_results.append(result)

    return unique_results


def hybrid_predictions(
    user_query_primary: str, 
    user_query_secondary: str = None, 
    lca_lifecycle_stage: str = None, 
    iso_code: str = None
) -> List[MatchedEF]:
    """
    Main function for the multi-phase approach.
    Returns a list of MatchedEF objects with phase information.
    """
    # Initialize results dictionary
    results_dict = {}
    
    # Phase 1: Base Search
    from emissions_factor_matching.dataset import search_emissions_factors_vectordb
    
    # Always do base search first
    base_results = search_emissions_factors_vectordb(user_query_primary, iso_code=iso_code, filter_metadata={})
    results_dict["phase_1_base"] = base_results
    
    # Check if we need Phase 2
    has_good_matches = any(candidate.similarity_score >= 0.8 for candidate in base_results)
    
    if has_good_matches:
        # Good matches found, prepare and return results
        return prepare_results(results_dict, modifier_used=False, isic_codes=[])
    
    # Phase 2: Enhanced Search with modifiers and ISIC codes
    # Extract modifiers
    modifiers = spot_modifiers(user_query_primary, lca_lifecycle_stage, iso_code)
    
    # Get ISIC codes
    isic_codes = map_isic_classification(user_query_primary, iso_code)
    
    # Search with modifiers if found
    if modifiers:
        modifier_results = []
        for modifier in modifiers:
            results = search_emissions_factors_vectordb(modifier, iso_code=iso_code, filter_metadata={})
            modifier_results.extend(results)
        
        # Remove duplicates based on activity_uuid
        seen_uuids = set()
        unique_modifier_results = []
        for result in modifier_results:
            if result.activity_uuid not in seen_uuids:
                seen_uuids.add(result.activity_uuid)
                unique_modifier_results.append(result)
        
        results_dict["phase_2_modifiers"] = unique_modifier_results
    
    # Search with ISIC codes if found
    if isic_codes:
        isic_results = []
        for isic_code in isic_codes:
            # Create filter for ISIC code
            filter_metadata = {"isic_code": isic_code}
            results = search_emissions_factors_vectordb(
                user_query_primary, 
                iso_code=iso_code, 
                filter_metadata=filter_metadata
            )
            isic_results.extend(results)
        
        # Remove duplicates
        seen_uuids = set()
        unique_isic_results = []
        for result in isic_results:
            if result.activity_uuid not in seen_uuids:
                seen_uuids.add(result.activity_uuid)
                unique_isic_results.append(result)
        
        results_dict["phase_2_isic"] = unique_isic_results
    
    # Prepare final results
    return prepare_results(results_dict, modifier_used=bool(modifiers), isic_codes=isic_codes)


def search_emissions_factors(user_input: str) -> List[MatchedEF]:
    """Legacy function for backward compatibility"""
    return hybrid_predictions(user_query_primary=user_input)


def match_emission_factor(
    user_query: str,
    activity_name: str, 
    geography: str = None,
    lca_stage: str = None
) -> dict:
    """Legacy function for backward compatibility"""
    # This is a simplified version - actual implementation would need more logic
    results = hybrid_predictions(
        user_query_primary=user_query,
        lca_lifecycle_stage=lca_stage,
        iso_code=geography
    )
    
    if results:
        top_match = results[0]
        return {
            "activity_uuid": top_match.activity_uuid,
            "confidence": "high" if top_match.similarity_score > 0.8 else "medium",
            "match_explanation": f"Matched with similarity score {top_match.similarity_score}"
        }
    return None


def get_closest_match(
    prompt: str,
    data: List[tuple]
) -> dict | None:
    """
    Gets the closest match from a list of candidates.
    Now uses structured output with LangChain.
    """
    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": json.dumps(data, ensure_ascii=False, indent=2)
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=ClosestMatchResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    if result:
        return {
            "activity_uuid": result.activity_uuid,
            "confidence": result.confidence,
            "match_explanation": result.match_explanation
        }
    else:
        logger.warning("Failed to get structured response for closest match")
        return None


async def get_technological_representation(process_name: str, activity_name: str):
    """
    Predict the Technological representativeness.
    Now uses structured output with LangChain.
    """
    prompt = (
        "You will be given a user's manufacturing process or technology and a target ecoinvent activity name.\n"
        "Your task is to determine if they represent similar technologies or processes.\n\n"
        
        "Consider these aspects:\n"
        "- Manufacturing methods and processes\n"
        "- Technology level (modern vs traditional)\n"
        "- Scale of operation\n"
        "- Core transformation process\n\n"
        
        "Respond with:\n"
        "- reason: A brief explanation of why they are similar or different\n"
        "- similar: true if they represent similar technologies, false otherwise\n\n"
        
        "Examples:\n"
        "User: 'CNC machining of aluminum parts'\n"
        "Activity: 'metal working, average for aluminium product manufacturing'\n"
        "Response: {\"reason\": \"Both involve aluminum processing, though CNC is more specific than average metal working\", \"similar\": true}\n\n"
        
        "User: 'Hand weaving of cotton fabric'\n"  
        "Activity: 'textile weaving, industrial scale'\n"
        "Response: {\"reason\": \"Hand weaving is traditional/artisanal while industrial weaving uses modern machinery at scale\", \"similar\": false}\n"
    )

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": f"User process: '{process_name}'\nActivity name: '{activity_name}'"
        }
    ]

    # Use async structured completion
    async with get_openai_async_client() as client:
        result = await get_async_structured_completion(
            messages=messages,
            response_model=TechnologicalRepresentationResponse,
            deployment=config.azure_openai_deployment,
            temperature=0,
        )

    if result:
        return {
            "reason": result.reason,
            "similar": result.similar
        }
    else:
        logger.warning("Failed to get structured response for technological representation")
        return None


def ask_anthropic(prompt: str):
    # Placeholder function - actual implementation would use Anthropic API
    return "Not implemented"


def eol_activity_lookup(
    user_query: str, 
    iso_code: str = None,
    limit: int = 10
) -> List[MatchedEF]:
    """
    Searches for End-of-Life (EoL) activities based on user query.
    Uses the EOL-specific vector database.
    """
    from emissions_factor_matching.dataset import search_emissions_factors_vectordb_eol
    
    # Search in EOL database
    results = search_emissions_factors_vectordb_eol(
        user_query, 
        iso_code=iso_code,
        filter_metadata={},
        limit=limit
    )
    
    # Convert to MatchedEF format
    matched_results = []
    for candidate in results:
        matched_results.append(
            MatchedEF(
                activity_uuid=candidate.activity_uuid,
                similarity_score=candidate.similarity_score,
                ef_activity_name=candidate.ef_activity_name,
                ef_reference_unit=candidate.ef_reference_unit,
                phase="eol_search",
                modifier_used=False,
                isic_used=False,
            )
        )
    
    return matched_results


def re_rank_candidates(
    request_model: Any,
    candidates: List[Candidate],
    augmented_query: str,
    enhanced_category: str,
    modifiers: List[str],
    isic_codes: List[str]
) -> MatchedEF | None:
    """
    Phase 1.7: LLM Re-ranking & Justification
    Use LLM to re-rank and select the best candidate from vector search results.
    Now uses structured output with LangChain.
    """
    # Convert candidates to dict format for prompt
    candidates_list = []
    for candidate in candidates:
        candidates_list.append({
            "activity_uuid": candidate.activity_uuid,
            "activity_name": candidate.activity_name,
            "reference_product_name": candidate.reference_product_name,
            "product_information": candidate.product_information,
            "source": candidate.source,
            "isic_4_code": candidate.isic_4_code,
            "geography": candidate.geography,
            "unit": candidate.unit,
            "distance": candidate.distance,
            "similarity_score": candidate.similarity_score
        })
    
    # Extract user query from request
    user_query = getattr(request_model, 'user_query_primary', '')
    if hasattr(request_model, 'user_query_secondary') and request_model.user_query_secondary:
        user_query += f" | {request_model.user_query_secondary}"
    
    # Build the user prompt with context
    context_parts = [
        f"USER REQUEST: {user_query}",
        f"ENHANCED CATEGORY: {enhanced_category}",
        f"MODIFIERS: {', '.join(modifiers) if modifiers else 'None'}",
        f"ISIC CODES: {', '.join(isic_codes) if isic_codes else 'None'}",
        f"AUGMENTED QUERY: {augmented_query}",
    ]

    # Add optional context
    if hasattr(request_model, 'lca_lifecycle_stage') and request_model.lca_lifecycle_stage:
        context_parts.append(f"LCA LIFECYCLE STAGE: {request_model.lca_lifecycle_stage}")
    if hasattr(request_model, 'iso_code') and request_model.iso_code:
        context_parts.append(f"ISO COUNTRY CODE: {request_model.iso_code}")

    # Add candidates information
    context_parts.append("\nCANDIDATES TO ANALYZE:")
    for i, candidate in enumerate(candidates_list, 1):
        context_parts.append(f"""
{i}. UUID: {candidate['activity_uuid']}
   Activity: {candidate['activity_name']}
   Product: {candidate['reference_product_name']}
   Info: {candidate.get('product_information', 'N/A')}
   Source: {candidate['source']}
   ISIC: {candidate.get('isic_4_code', 'N/A')}
   Geography: {candidate.get('geography', 'N/A')}
   Unit: {candidate.get('unit', 'N/A')}
   Distance: {candidate['distance']:.4f}
   Similarity: {candidate['similarity_score']:.4f}""")

    context_parts.append("\nPlease analyze these candidates and select the best match using the required JSON format.")

    user_prompt = "\n".join(context_parts)

    messages = [
        {
            "role": "system",
            "content": get_llm_reranking_prompt()
        },
        {
            "role": "user",
            "content": user_prompt
        }
    ]
    
    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=RerankingResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )
    
    if not result:
        logger.error("Failed to get re-ranking response from LLM")
        return None
    
    # Find the selected candidate
    selected_candidate = None
    for candidate in candidates:
        if candidate.activity_uuid == result.selected_candidate_uuid:
            selected_candidate = candidate
            break
    
    if not selected_candidate:
        logger.error(f"Selected candidate UUID {result.selected_candidate_uuid} not found in candidates list")
        return None
    
    # Create MatchedEF object from the selected candidate and LLM response
    matched_ef = MatchedEF(
        activity_uuid=selected_candidate.activity_uuid,
        activity_name=selected_candidate.activity_name,
        reference_product_name=selected_candidate.reference_product_name,
        product_information=selected_candidate.product_information,
        source=selected_candidate.source,
        geography=selected_candidate.geography,
        unit=selected_candidate.unit,
        confidence=result.confidence,
        confidence_score=result.confidence_score,
        explanation=result.explanation,
        original_distance=selected_candidate.distance,
        final_rank=1,  # Since this is the selected candidate
        processing_metadata={
            "enhanced_category": enhanced_category,
            "modifiers": modifiers,
            "isic_codes": isic_codes,
            "augmented_query": augmented_query,
            "ranking_rationale": result.ranking_rationale,
            "alternative_considerations": result.alternative_considerations
        }
    )
    
    logger.info(f"Re-ranking complete: Selected '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")
    
    return matched_ef


def augment_query_text(
    request: Any,
    enhanced_category: str,
    modifiers: List[str],
    isic_codes: List[str]
) -> str:
    """
    Phase 1.4: Query Text Augmentation
    Transforms structured query information into optimized search text for vector similarity matching.
    Uses LangChain with structured output for consistency.
    """
    from emissions_factor_matching.prompt import get_query_augmentation_prompt
    from pydantic import BaseModel, Field
    
    class AugmentedQueryResponse(BaseModel):
        """Response model for query augmentation"""
        augmented_query: str = Field(
            description="The optimized search query text for emission factor matching"
        )
    
    # Build context from request
    context_parts = []
    
    # Add primary query
    if hasattr(request, 'user_query_primary'):
        context_parts.append(request.user_query_primary)
    
    # Add secondary query if available
    if hasattr(request, 'user_query_secondary') and request.user_query_secondary:
        context_parts.append(request.user_query_secondary)
    
    # Add lifecycle stage context
    if hasattr(request, 'lca_lifecycle_stage') and request.lca_lifecycle_stage:
        context_parts.append(f"lifecycle stage: {request.lca_lifecycle_stage}")
    
    # Create the augmentation prompt
    prompt = get_query_augmentation_prompt()
    
    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": f"""Enhanced Category: {enhanced_category}
Modifiers: {', '.join(modifiers) if modifiers else 'None'}
ISIC Codes: {', '.join(isic_codes) if isic_codes else 'None'}
User Query Context: {' | '.join(context_parts)}

Generate an optimized search query for emission factor matching."""
        }
    ]
    
    # Use structured completion for consistency
    result = get_structured_completion(
        messages=messages,
        response_model=AugmentedQueryResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )
    
    if result:
        logger.info(f"Query augmentation successful: '{result.augmented_query}'")
        return result.augmented_query
    else:
        # Fallback to simple concatenation
        fallback_query = ' '.join(context_parts + modifiers)
        logger.warning(f"Query augmentation failed, using fallback: '{fallback_query}'")
        return fallback_query


def construct_dynamic_filters(
    request: Any,
    enhanced_category: str,
    modifiers: List[str],
    isic_codes: List[str]
) -> Dict[str, Any]:
    """
    Phase 1.5: Dynamic Filter Construction
    Builds ChromaDB filters based on category, modifiers, and ISIC codes.
    Uses dynamic ISIC section determination instead of hard-coded mappings.
    """
    filters = {}
    conditions = []
    
    # Always filter by activity type
    activity_type_filter = {"activity_type": {"$eq": "ordinary transforming activity"}}
    conditions.append(activity_type_filter)
    
    # Handle ISIC-based filtering dynamically
    if isic_codes:
        # Get unique ISIC classifications from the database
        available_isic_classifications = set(efs_with_geographies['ISIC Classification'].dropna().unique())
        
        # Find matching ISIC codes in ChromaDB format
        matching_classifications = []
        for code in isic_codes:
            # Match both exact code and code with description (e.g., "4923:Freight transport by road")
            for classification in available_isic_classifications:
                # Convert classification to string to handle both int and string types
                classification_str = str(classification)
                if classification_str.startswith(f"{code}:") or classification_str == code:
                    matching_classifications.append(classification)
        
        if matching_classifications:
            if len(matching_classifications) == 1:
                conditions.append({"ISIC Classification": {"$eq": matching_classifications[0]}})
            else:
                conditions.append({"ISIC Classification": {"$in": matching_classifications}})
            
            # Dynamically determine ISIC section from the matched codes
            # Get the sections for these ISIC codes from the database
            matched_sections = set()
            for classification in matching_classifications:
                # Convert classification to string to handle both int and string types
                classification_str = str(classification)
                # Extract just the code part if it's in "code:description" format
                code = classification_str.split(':')[0] if ':' in classification_str else classification_str
                
                # Find the section for this code in the database
                # Convert ISIC Classification to string to handle both int and string types
                isic_classification_str = efs_with_geographies['ISIC Classification'].astype(str)
                section_matches = efs_with_geographies[
                    isic_classification_str.str.startswith(f"{code}:")
                ]['ISIC Section'].dropna().unique()
                
                matched_sections.update(section_matches)
            
            # Add section filter if we found matching sections
            if matched_sections:
                if len(matched_sections) == 1:
                    conditions.append({"isic_section": {"$eq": list(matched_sections)[0]}})
                else:
                    conditions.append({"isic_section": {"$in": list(matched_sections)}})
    
    # If no ISIC codes provided, use category hints for section filtering
    elif enhanced_category:
        # Use category prefix patterns to suggest likely sections
        # This is more flexible than hard-coding exact mappings
        category_hints = {
            "TRANSPORT": "H - Transportation and storage",
            "ENERGY": "D - Electricity, gas, steam and air conditioning supply",
            "WASTE": "E - Water supply; sewerage, waste management and remediation activities",
            "CHEMICAL": "C - Manufacturing",
            "PRODUCT": "C - Manufacturing",
            "CONSTRUCTION": "F - Construction",
        }
        
        for hint, section in category_hints.items():
            if hint in enhanced_category:
                # Only add as a hint, not a strict filter
                # This allows for flexibility if activities exist in other sections
                logger.info(f"Adding section hint for {hint}: {section}")
                conditions.append({"isic_section": {"$eq": section}})
                break
    
    # Add unit filters if specified
    if hasattr(request, 'valid_units') and request.valid_units:
        conditions.append({"unit": {"$in": request.valid_units}})
    
    # Add LCIA method filter if specified
    if hasattr(request, 'lcia_method') and request.lcia_method and not getattr(request, 'carbon_only', False):
        conditions.append({request.lcia_method: {"$eq": True}})
    
    # Handle specific modifiers that might affect filtering
    if modifiers:
        # Example: If we have weight modifiers for transport, we could add specific filters
        weight_modifiers = [m for m in modifiers if 't' in m and any(c.isdigit() for c in m)]
        if weight_modifiers and "TRANSPORT" in enhanced_category:
            # This is an example of dynamic modifier-based filtering
            logger.info(f"Found weight modifiers for transport: {weight_modifiers}")
            # Could add specific filters based on weight ranges if needed
    
    # Construct final filter
    if len(conditions) == 0:
        filters = {}
    elif len(conditions) == 1:
        filters = conditions[0]
    else:
        filters = {"$and": conditions}
    
    logger.info(f"Constructed dynamic filters with {len(conditions)} conditions")
    logger.debug(f"Filter details: {filters}")
    
    return filters