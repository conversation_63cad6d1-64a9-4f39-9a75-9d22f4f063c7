import numpy as np
from typing import Optional
from pydantic import BaseModel
from fastapi import (
    FastAPI,
    Head<PERSON>,
    Depends,
    Query,
    HTTPException,
)
from torch import tensor
import json
from emissions_factor_matching.dataset import collection, collection_eol
from emissions_factor_matching.predictions import (
    get_cas_number,
    predict_input_category,
    predict_product_constituents,
    get_common_chemical_names,
    get_product_description,
    get_product_activity_isic_section,
    get_closest_match,
    get_technological_representation,
)
from shared_predictions import get_top_match
from emissions_factor_matching.geography import get_geography_activity_match
from utils import logger
from utils.cache import create_cache, cached
import asyncio

cache = create_cache(prefix='ef_')

def get_api_version(api_version: Optional[str] = Header(default="latest")) -> str:
    return api_version

model_api = FastAPI()

def query_eol_activities(material: str, disposal_method: str, number_of_results: int = 25):
    where_document = (
        {"$contains":"Recycled Content cut-off"}
        if disposal_method == "recycling"
        else None
    )

    return collection_eol.query(
        query_texts=[f"{material} {disposal_method}"],
        n_results=number_of_results,
        where_document=where_document
    )

class ActivityRequest(BaseModel):
    chemical_name: str
    cas_number: str | None = None

class Activity(BaseModel):
    activity_uuid: str
    activity_name: str
    reference_product_name: str
    product_information: str
    source: str
    geography: str | None = None

class ActivityResponse(Activity):
    similarity: float | None = None

class ActivityRecommendationsRequest(BaseModel):
    chemical_name: str
    valid_units: list[str] | None = None
    product_category: str | None = None
    cas_number: str | None = None
    iso_code: str | None = None
    number_of_matches: int | None = None
    lcia_method: str | None = None
    carbon_only: bool | None = None

class ActivityRecommendationsResponse(BaseModel):
    matched_activity: Activity
    confidence: str
    explanation: str
    recommendations: list[ActivityResponse]

def get_activity_from_dataset(
    activity_name: str,
    iso_code: str,
    similarity: float,
    reference_product_name: str,
) -> ActivityResponse:
    activity = get_geography_activity_match(
        activity_name,
        iso_code,
        reference_product_name
    )

    return ActivityResponse(
        activity_name=activity["Activity Name"],
        activity_uuid=activity["Activity UUID"],
        reference_product_name=activity["Reference Product Name"],
        product_information=activity["Product Information"],
        source=activity["Source"],
        geography=activity["Geography"],
        similarity=similarity
    )

@model_api.get("/search")
@cached(cache)
def search_activities(
    query: str = Query(..., description="The search query"),
    geography_iso3: str = Query(default="GLO", description="The geography to search in"),
    number_of_results: int = Query(default=25, description="The number of results to return"),
    api_version: str = Depends(get_api_version),
) -> list[ActivityResponse]:
    logger.info(f"API Version: {api_version}")

    where = {"activity_type": {"$eq": "ordinary transforming activity"}}
    documents = collection.query(
        query_texts=[query],
        n_results=number_of_results,
        where=where,
    )

    activities = [
        get_activity_from_dataset(
            activity_name=documents["documents"][0][i],
            iso_code=geography_iso3,
            similarity=0,
            reference_product_name=documents["metadatas"][0][i]["reference_product_name"]
        )
        for i in range(len(documents["ids"][0]))
    ]

    return activities

class EolActivityRecommendationsRequest(BaseModel):
    material: str
    disposal_method: str
    geography: str = "RoW"

@model_api.post("/eol/activity")
@cached(cache)
async def get_eol_recommended_activity(
    activity_request: EolActivityRecommendationsRequest,
    api_version: str = Depends(get_api_version),
):
    logger.info(f"API Version: {api_version}")

    documents = query_eol_activities(
        material=activity_request.material,
        disposal_method=activity_request.disposal_method,
    )

    activities = []
    activities_for_selection = []
    for i in range(len(documents["ids"][0])):
        activity = {
            "uuid": documents["metadatas"][0][i]["uuid"],
            "activity_name": documents["documents"][0][i],
            "metadata": documents["metadatas"][0][i],
            "similarity": 0,
        }
        activities.append(activity)
        activities_for_selection.append({
            k: v
            for k, v in activity.items()
            if k != "similarity"
        })

    closest_match = await get_closest_match(
        f"{activity_request.material} ({activity_request.disposal_method})",
        activities_for_selection,
    )

    matched_activity = next(
        (
            get_activity_from_dataset(
                activity["activity_name"],
                activity_request.geography,
                activity["similarity"],
                activity["metadata"]["reference_product_name"]
            )
            for activity in activities
            if closest_match.get("activity_uuid") == activity["uuid"]
        ),
        None,
    )

    return matched_activity

@model_api.post("/activities/recommendations")
@cached(cache)
async def get_recommended_activities(
    activity_request: ActivityRecommendationsRequest,
    api_version: str = Depends(get_api_version),
):
    logger.info(f"API Version: {api_version}")

    if not activity_request.cas_number:
        activity_request.cas_number = get_cas_number(activity_request.chemical_name)

    logger.info(activity_request.cas_number)

    number_of_matches = activity_request.number_of_matches or 5
    iso_code = activity_request.iso_code or "GLO"
    material = activity_request.chemical_name
    category = predict_input_category(material)

    if category == "PRODUCT":
        if activity_request.product_category:
            material += f" for {activity_request.product_category}"

        constituent = predict_product_constituents(material)
        query_text = f"{constituent} {material}"
    else:
        common_names = get_common_chemical_names(material)
        logger.warning(common_names)

        description = get_product_description(material)
        logger.warning(description)

        query_text = (
            f"{material} ({common_names}): {description}"
            if common_names != "NONE"
            else f"{material}: {description}"
        )

    where = {"activity_type": {"$eq": "ordinary transforming activity"}}
    product_section = get_product_activity_isic_section(material)
    if product_section:
        where = {
            "$and": [
                {"activity_type": {"$eq": "ordinary transforming activity"}},
                {"isic_section": {"$eq": product_section}},
            ],
        }

    if activity_request.valid_units:
        if "$and" in where:
            where["$and"].append({"unit": {"$in": activity_request.valid_units}})
        else:
            where = {
                "$and": [
                    {"activity_type": {"$eq": "ordinary transforming activity"}},
                    {"unit": {"$in": activity_request.valid_units}},
                ],
            }
    
    if activity_request.lcia_method and not activity_request.carbon_only:
        if "$and" in where:
            where["$and"].append({activity_request.lcia_method: {"$eq": True}})
        else:
            where = {
                "$and": [
                    {"activity_type": {"$eq": "ordinary transforming activity"}},
                    {activity_request.lcia_method: {"$eq": True}},
                ],
            }

    documents = collection.query(
        query_texts=[query_text],
        n_results=25,
        where=where
    )

    activities = []
    activities_for_selection = []
    for i in range(len(documents["ids"][0])):
        activity = {
            "uuid": documents["metadatas"][0][i]["uuid"],
            "activity_name": documents["documents"][0][i],
            "metadata": documents["metadatas"][0][i],
            "similarity": 0,
        }
        activities.append(activity)
        activities_for_selection.append({
            k: v
            for k, v in activity.items()
            if k != "similarity"
        })

    closest_match = await get_closest_match(
        query_text,
        activities_for_selection,
    )

    matched_activity = next(
        (
            get_activity_from_dataset(
                activity["activity_name"],
                iso_code,
                activity["similarity"],
                activity["metadata"]["reference_product_name"]
            )
            for activity in activities
            if closest_match.get("activity_uuid") == activity["uuid"]
        ),
        None,
    )

    recommendations = [
        get_activity_from_dataset(
            activity["activity_name"],
            iso_code,
            activity["similarity"],
            activity["metadata"]["reference_product_name"]
        )
        for activity in activities
        if closest_match.get("activity_uuid") != activity["uuid"]
    ]

    return ActivityRecommendationsResponse(
        matched_activity=matched_activity,
        confidence=closest_match["confidence"],
        explanation=closest_match["match_explanation"],
        recommendations=recommendations,
    )

@model_api.get("/geography-match")
@cached(cache)
def geography_match(
    activity_name: str,
    reference_product_name: str,
    target_geography_iso: str,
    api_version: str = Depends(get_api_version),
) -> Activity:
    activity = get_geography_activity_match(
        activity_name,
        target_geography_iso,
        reference_product_name
    )

    return Activity(
        activity_name=activity["Activity Name"],
        activity_uuid=activity["Activity UUID"],
        reference_product_name=activity["Reference Product Name"],
        product_information=activity["Product Information"],
        source=activity["Source"],
        geography=activity["Geography"],
    )

@model_api.post("/cache/clear")
def clear_cache():
    """Clear the entire cache."""
    cache.clear()
    return {"message": "Cache cleared successfully"}

@model_api.get("/cache/stats")
def get_cache_stats():
    """Get cache statistics."""
    volume = cache.volume()
    size_kb = volume / 1024 if volume is not None else 0
    try:
        num_items = len(cache)
    except (TypeError, NotImplementedError):
        num_items = "Not available for this cache type"
    
    return {
        "size (KB)": size_kb,
        "number of items": num_items,
        "directory": cache.directory
    }

@model_api.get("/match-quality")
@cached(cache)
async def match_quality(process_name: str = Query(...), matched_activity_name: str = Query(...)):
    technology_match = await get_technological_representation(process_name, matched_activity_name)

    return {"match_quality": technology_match}